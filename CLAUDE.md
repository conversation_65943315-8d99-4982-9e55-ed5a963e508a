# Orders Tracker Application

## Overview
The Orders Tracker is a comprehensive Next.js application designed to manage and track purchase orders, handle notifications, and manage todos for an e-commerce platform. The application provides administrators with tools to monitor order statuses, shipping information, and manage follow-up tasks.

## Architecture

### Tech Stack
- **Frontend**: Next.js 14 with TypeScript
- **UI Components**: HeroUI (Next-generation React UI library)
- **State Management**: Zustand with persistence
- **Styling**: Tailwind CSS
- **Authentication**: NextAuth.js
- **Date/Time**: Moment.js
- **Real-time Communication**: Socket.io
- **Icons**: Lucide React

### Key Components

#### Store Management (Zustand)
- **Purchase Store** (`src/store/purchaseStore.ts`): Manages purchase data, filters, and statuses
- **Todo Store** (`src/store/todoStore.ts`): Handles todo items, creation, updates, and filtering
- **Notification Store** (`src/store/notificationStore.ts`): Manages real-time notifications
- **User Store** (`src/store/userStore.ts`): User authentication and profile data

#### Core Features

##### 1. Purchase Management
- **Location**: `src/app/(authenticated)/purchases/`
- **Components**: 
  - `PurchaseTable.tsx`: Main table displaying purchases with filtering and search
  - `columns.tsx`: Table column definitions and cell renderers
  - `AddPurchase.tsx`, `EditPurchase.tsx`: Forms for purchase management
- **Features**:
  - Advanced filtering (source, multiple licenses, shipping status)
  - Search across all purchase fields
  - Pagination and sorting
  - Purchase status tracking (activation, shipping, training)

##### 2. Notification System
- **Location**: `src/components/notifications/`
- **Components**:
  - `Notifications.tsx`: Main notification drawer with real-time updates
  - `CreateTodoModal.tsx`: Modal for converting notifications to todos
- **Features**:
  - Real-time notifications via WebSocket
  - Two notification types: Shipping Missing and Todo Reminders
  - Bulk todo creation from shipping notifications
  - Selection interface for missing shipments

##### 3. Todo Management
- **Location**: `src/components/todos/`
- **Components**:
  - `TodoList.tsx`: Main todo interface with filtering and management
  - `TodoForm.tsx`: Form for creating/editing todos
  - `TodoStats.tsx`: Dashboard statistics
- **Features**:
  - Priority levels (LOW, MEDIUM, HIGH, URGENT)
  - Assignment to admin users
  - Due dates and reminder notifications
  - Status tracking (PENDING, IN_PROGRESS, COMPLETED, CANCELLED)
  - Tags and metadata support

## Key Functionality

### Notification-to-Todo Workflow
1. **Notification Reception**: System receives shipping missing notifications
2. **Purchase Selection**: Users can select multiple purchases from notification drawer
3. **Bulk Todo Creation**: Selected purchases are converted to todos with:
   - Automatic title generation
   - Detailed description with purchase information
   - Assignee and reminder date selection
   - Relevant tags and metadata

### Data Flow
```
Backend API → WebSocket → Notification Store → UI Components
                     ↓
                Purchase Store → Purchase Table/Filters
                     ↓
            CreateTodoModal → Todo Store → Todo List
```

### Authentication & Authorization
- NextAuth.js integration for secure authentication
- Role-based access control for admin functions
- Session management with JWT tokens
- Protected routes for authenticated users

## File Structure
```
src/
├── app/
│   ├── (authenticated)/
│   │   ├── purchases/         # Purchase management pages
│   │   ├── todos/             # Todo management pages
│   │   └── dashboard/         # Dashboard and analytics
│   ├── (auth)/               # Authentication pages
│   └── api/                  # API routes
├── components/
│   ├── notifications/        # Notification system
│   ├── purchases/           # Purchase-related components
│   ├── todos/               # Todo management components
│   └── navigation/          # Navigation and layout
├── store/                   # Zustand stores
├── lib/                     # Utilities and configuration
└── types/                   # TypeScript type definitions
```

## Configuration

### Environment Variables
- `CLOUDRUN_DEV_URL`: Backend API base URL
- `NEXTAUTH_SECRET`: Authentication secret
- `NEXTAUTH_URL`: Application URL

### Key Features Implemented
1. **Real-time Notifications**: WebSocket integration for live updates
2. **Advanced Filtering**: Complex filter system for purchases and todos
3. **Bulk Operations**: Select multiple items for batch processing
4. **Responsive Design**: Mobile-friendly interface with HeroUI
5. **State Persistence**: Zustand persistence for offline capability
6. **Type Safety**: Full TypeScript implementation

## Usage Patterns

### Creating Todos from Notifications
1. Open notification drawer by clicking bell icon
2. Navigate to "Notifications" tab
3. Select desired purchases using checkboxes
4. Click "Create X Todo(s)" button
5. Fill in assignment and reminder details
6. Submit to create todos for selected purchases

### Managing Purchases
1. Navigate to `/purchases`
2. Use filter controls for refined searching
3. Click on purchase rows to view details
4. Use action buttons for editing or viewing more information

### Todo Management
1. Navigate to `/todos`
2. View statistics dashboard
3. Filter todos by status, priority, due date
4. Create new todos or edit existing ones
5. Track progress and completion

## Development Notes

### State Management
- Uses Zustand for lightweight, TypeScript-friendly state management
- Implements persistence middleware for data retention
- Separate stores for different domains (purchases, todos, notifications)

### UI Components
- HeroUI provides modern, accessible components
- Consistent design system throughout application
- Responsive layouts with Tailwind CSS
- Dark/light theme support

### API Integration
- RESTful API communication for CRUD operations
- WebSocket for real-time updates
- Error handling and loading states
- Optimistic updates for better UX

This application serves as a comprehensive order management system with advanced notification handling and task management capabilities.